<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SiteMovement;

class SiteMovementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Site Kapatma/Açma Ayarları
        SiteMovement::create([
            'id' => 1,
            'name' => 'Site Kapatma/Açma',
            'extra1' => '0', // 0: Site açık, 1: Site kapalı
            'extra2' => null,
            'extra3' => null,
            'extra4' => null,
            'image' => null,
        ]);

        // Mail Gönderme Ayarları
        SiteMovement::create([
            'id' => 2,
            'name' => 'Mail Gönderme Ayarları',
            'extra1' => '<EMAIL>', // Mail adresi
            'extra2' => 'Demo WTW', // Gönderen adı
            'extra3' => null,
            'extra4' => null,
            'image' => null,
        ]);

        // Toplu Mail Gönderimi Ayarları
        SiteMovement::create([
            'id' => 3,
            'name' => 'Toplu Mail Gönderimi',
            'extra1' => '<EMAIL>', // Mail adresi
            'extra2' => 'Demo WTW', // Gönderen adı
            'extra3' => null,
            'extra4' => null,
            'image' => null,
        ]);
    }
}
