<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('benefits', function (Blueprint $table) {
            $table->string('desc_image')->nullable()->after('description_en');
            $table->string('desc_image_en')->nullable()->after('desc_image');
            $table->string('alert_bg_color')->nullable()->after('alert_color');
            $table->string('alert_bg_color_en')->nullable()->after('alert_bg_color');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('benefits', function (Blueprint $table) {
            $table->dropColumn(['desc_image', 'desc_image_en', 'alert_bg_color', 'alert_bg_color_en']);
        });
    }
};
